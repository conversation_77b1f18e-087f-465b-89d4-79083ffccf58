using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Diagnostics;
using MMS.Core.CoreUTI;
using MMS.Core.Services.Base;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;

namespace MMS.Core.dbContext
{
    public class DbContextFactory : IDbContextFactory
    {
        private readonly static ConcurrentDictionary<string, MMSContext> _dbContextDic = new();
        private readonly string _currentSectionId;
        private readonly MMSContext _scopedContext;

        public DbContextFactory(MMSContext scopedContext)
        {
            _scopedContext = scopedContext;

            if (HttpContext.Current != null)
            {
                _currentSectionId = HttpContext.Current.Session.Id;
            }
            else
            {
                _currentSectionId = null;
            }
        }

        public MMSContext GetContext(bool isUsingSessionContext)
        {
            if (!isUsingSessionContext)
            {
                // Return the injected scoped context for consistency with DI pattern
                return _scopedContext;
            }

            var result = _dbContextDic.GetValueOrDefault(_currentSectionId); // Get session ctx

            if (result == null)
            {
                result = this.CreateDbContext();
                _dbContextDic.TryAdd(_currentSectionId, result);
            }

            return result;
        }

        public void CtxDisposeOrNot(MMSContext context, bool isUsingSessionContext)
        {
            if (!isUsingSessionContext)
            {
                // Don't dispose the scoped context as it's managed by DI container
                // Only dispose if it's not the injected scoped context
                if (context != _scopedContext)
                {
                    context.Dispose();
                }
            }
        }

        private MMSContext CreateDbContext()
        {
            return CreateDbContext(null);
        }

        public void RemoveContext()
        {
            if (string.IsNullOrWhiteSpace(_currentSectionId))
                return;
            _dbContextDic.Remove(_currentSectionId, out var mmsContext);
        }

        public MMSContext CreateDbContext(string[] args)
        {
            var builder = new DbContextOptionsBuilder<MMSContext>();
            builder.ConfigureWarnings(warnings => warnings.Ignore(CoreEventId.NavigationBaseIncludeIgnored, CoreEventId.NavigationBaseIncluded));

            var connectionString = Environment.GetEnvironmentVariable(MMS.Core.CoreUTI.Constants.ENV_MMS_DB_CONNECTION);

#if DEBUG
            // Fake connection if debug for by pass dotnet ef migrations add
            if (string.IsNullOrWhiteSpace(connectionString))
                connectionString = "Server=localhost;Port=3306;Database=mms;User=root;Password=password;";
#else
            if (string.IsNullOrWhiteSpace(connectionString)) throw new InvalidOperationException($"Can not find the MMS_DB_CONNECTION");
#endif

            var serverVersion = new MariaDbServerVersion(new Version(8, 0));

            builder.UseMySql(connectionString,
                serverVersion,
                providerOptions => providerOptions.EnableRetryOnFailure(
                                maxRetryCount: 10,
                                maxRetryDelay: TimeSpan.FromSeconds(30),
                                errorNumbersToAdd: null)
                                .CommandTimeout(120)
                );

            var ctx = new MMSContext(builder.Options);
            return ctx;
        }
    }
}
