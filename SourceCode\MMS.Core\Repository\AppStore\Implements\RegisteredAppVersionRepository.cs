﻿using Microsoft.EntityFrameworkCore;
using MMS.Core.CoreUTI;
using MMS.Core.dbContext;
using MMS.Core.Entities;
using MMS.Core.Repository.Impl;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace MMS.Core.Repository.AppStore.Implements
{
    public class RegisteredAppVersionRepository : BaseRepository<RegisteredAppVersions>, IRegisteredAppVersionRepository
    {
        public RegisteredAppVersionRepository(DbContextFactory contextFactory) : base(contextFactory)
        {
        }

        public async Task<int?> GetLatestAppVersionIdAsync(int applicationId)
        {
            var context = GetContext();
            var appVersion = await context.Set<RegisteredAppVersions>()
                .Include(p => p.RegisteredApp)
                .Where(p =>
                    p.RegisteredAppId == applicationId &&
                    p.RegisteredApp.IsStatus == Constants.NOTCHANGE_RECORD)
                .OrderByDescending(r => r.Created)
                .FirstOrDefaultAsync();

            CtxDisposeOrNot(context);
            if (appVersion is null) return null;

            return appVersion.Id;
        }

        public async Task<string> GetCurrentAppVersionNumberAsync(int terminalMasterId, int applicationId)
        {
            var context = GetContext();
            var mapping = await context.Set<ApplicationDeviceMapping>()
                .FirstOrDefaultAsync(p =>
                    p.TerminalMasterId == terminalMasterId && p.ApplicationId == applicationId &&
                    p.IsStatus == Constants.NOTCHANGE_RECORD);

            if (mapping is null)
            {
                CtxDisposeOrNot(context);
                return string.Empty;
            }

            var appVersion = await context.Set<RegisteredAppVersions>().FirstOrDefaultAsync(p => p.Id == mapping.ApplicationVersionId);

            CtxDisposeOrNot(context);
            return appVersion.VersionNumber ?? string.Empty;
        }

		public async Task<List<RegisteredAppVersions>> GetByRegisteredAppIdAsync(int registeredAppId)
		{
            var context = GetContext();

            var query = context.RegisteredAppVersions.AsNoTracking()
                               .Where(p => p.RegisteredAppId == registeredAppId
                                        && p.IsStatus != Constants.DELETE_RECORD
                                        && p.IsStatus != Constants.PRE_DELETE_RECORD);

            var result = await query.ToListAsync();
            CtxDisposeOrNot(context);
            return result;
		}

        public async Task<List<int>> GetIdsByRegisteredAppIdAsync(int registeredAppId)
        {
            var context = GetContext();

            var query = context.RegisteredAppVersions.AsNoTracking()
                .Where(p => p.RegisteredAppId == registeredAppId
                            && p.IsStatus != Constants.DELETE_RECORD
                            && p.IsStatus != Constants.PRE_DELETE_RECORD).Select(p => p.Id);

            var result = await query.ToListAsync();
            CtxDisposeOrNot(context);
            return result;
        }

        public async Task<List<RegisteredAppVersions>> GetRegisteredAppVersionsByListIdsAsync(List<int> versionIds)
        {
            var context = GetContext();

            var query = context.RegisteredAppVersions
                .AsNoTracking()
                .Where(p => versionIds.Contains(p.Id)
                    && p.IsStatus != Constants.DELETE_RECORD
                    && p.IsStatus != Constants.PRE_DELETE_RECORD);

            var result = await query.ToListAsync();
            CtxDisposeOrNot(context);
            return result;
        }

        public async Task<ApplicationDeviceMapping> GetByTerminalAppAndVersion(int terminalMasterId, int applicationId, int versionId)
        {
            var context = GetContext();

            var result = await context.ApplicationDeviceMappings.AsNoTracking()
                .SingleOrDefaultAsync(p => p.TerminalMasterId == terminalMasterId
                    && p.ApplicationId == applicationId
                    && p.ApplicationVersionId == versionId
                    && p.IsStatus != Constants.DELETE_RECORD
                    && p.IsStatus != Constants.PRE_DELETE_RECORD);

            CtxDisposeOrNot(context);
            return result;
        }
    }
}
