﻿using Microsoft.EntityFrameworkCore;
using MMS.Core.CoreUTI;
using MMS.Core.CoreUTI.Enum;
using MMS.Core.dbContext;
using MMS.Core.Entities.Locations;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace MMS.Core.Repository.Impl
{
    public class LocationItemRepository : BaseRepository<LocationItem>, ILocationItemRepository
    {
        public LocationItemRepository(DbContextFactory contextFactory) : base(contextFactory)
        {
        }

        public async Task<IList<LocationItem>> GetLocationItemSource(int parentId, LocationLevel locationLevel)
        {
            var context = GetContext();

            var query = context.LocationItem.Where(o => o.IsStatus != Constants.DELETE_RECORD);

            if (parentId > 0)
            {
                query = query.Where(o => o.ParentId == parentId);
            }
            else
            {
                query = query.Where(o => o.Location.LocationLevel == locationLevel);
            }

            query = query.Include(o => o.Location);
            query = query.Include(o => o.AddressLocalItem);

            var result = await query.ToListAsync();
            CtxDisposeOrNot(context);
            return result;
        }

        public async Task<IList<int>> GetLocationAreaIdByParentIdAsync(int parentId)
        {
            var context = GetContext();
            var locationArea = await context.Set<Location>()
                .Where(p => p.LocationLevel == LocationLevel.LocationAreas
                            && p.IsStatus != Constants.DELETE_RECORD
                            && p.IsStatus != Constants.PRE_DELETE_RECORD
                            && p.IsActive).FirstAsync();

            var query = context.Set<LocationItem>()
                .Where(p => p.LocationId == locationArea.Id
                            && p.IsStatus != Constants.DELETE_RECORD
                            && p.IsStatus != Constants.PRE_DELETE_RECORD);
            query = query
                .Where(b => b.Hierarchy.StartsWith(parentId + ",")
                    || b.Hierarchy.EndsWith("," + parentId)
                    || b.Hierarchy.Contains("," + parentId + ",")
                    || b.Hierarchy.Equals(parentId));

            var result = await query.Select(p => p.Id).ToListAsync();
            CtxDisposeOrNot(context);
            return result;
        }

        public async Task<Dictionary<int, List<int>>> GetLocationAreaIdsByParentIdsAsync(List<int> parentIds)
        {
            var context = GetContext();
            var locationArea = await context.Set<Location>()
                .Where(p => p.LocationLevel == LocationLevel.LocationAreas
                            && p.IsStatus != Constants.DELETE_RECORD
                            && p.IsStatus != Constants.PRE_DELETE_RECORD
                            && p.IsActive)
                .FirstOrDefaultAsync();

            if (locationArea == null)
            {
                CtxDisposeOrNot(context);
                return new Dictionary<int, List<int>>();
            }

            var query = context.Set<LocationItem>()
                .Where(p => p.LocationId == locationArea.Id
                            && p.IsStatus != Constants.DELETE_RECORD
                            && p.IsStatus != Constants.PRE_DELETE_RECORD);

            var results = await query.ToListAsync();

            var groupedResults = parentIds
                .ToDictionary(
                    parentId => parentId,
                    parentId => results
                        .Where(item =>
                            item.Hierarchy.StartsWith(parentId + ",") ||
                            item.Hierarchy.EndsWith("," + parentId) ||
                            item.Hierarchy.Contains("," + parentId + ",") ||
                            item.Hierarchy.Equals(parentId.ToString()))
                        .Select(item => item.Id)
                        .ToList()
                );

            CtxDisposeOrNot(context);
            return groupedResults;
        }

        public async Task<LocationItem> GetLocationItemIncludeLevelAsync(int locationItemId)
        {
            var context = GetContext();
            var locationItem = await context.Set<LocationItem>()
                .Include(p => p.Location)
                .Include(p => p.AddressLocalItem)
                .FirstOrDefaultAsync(p => p.Id == locationItemId);

            CtxDisposeOrNot(context);
            return locationItem;
        }

        public async Task<IList<int>> GetAllLocationAreaIdsAsync()
        {
            var context = GetContext();
            var locationArea = await context.Set<Location>()
                .Where(p => p.LocationLevel == LocationLevel.LocationAreas
                            && p.IsStatus != Constants.DELETE_RECORD
                            && p.IsStatus != Constants.PRE_DELETE_RECORD
                            && p.IsActive).FirstAsync();

            var query = context.Set<LocationItem>()
                .Where(p => p.LocationId == locationArea.Id
                            && p.IsStatus != Constants.DELETE_RECORD
                            && p.IsStatus != Constants.PRE_DELETE_RECORD);

            var result = await query.Select(p => p.Id).ToListAsync();
            CtxDisposeOrNot(context);
            return result;
        }

        public async Task<IList<int>> GetLocationAreaIdByStringHierarchy(string locationIds)
        {
            if (string.IsNullOrEmpty(locationIds))
                return await GetAllLocationAreaIdsAsync();

            var intList = UtilConvert.SeparateStringToInt(locationIds);
            const LocationLevel startEnumLevel = LocationLevel.Region;
            const LocationLevel endEnumLevel = LocationLevel.LocationAreas;
            const int levelLength = endEnumLevel - startEnumLevel + 1;

            // only get Location Item id in Hierarchy ids
            if (intList.Count > levelLength)
                intList.RemoveRange(levelLength, intList.Count - levelLength);

            var lastId = intList[^1];

            // if last selected is Location id return it
            if (intList.Count == levelLength && lastId != 0)
            {
                var resultList = new List<int> { lastId };
                return resultList;
            }

            // find nearest id not equals 0 or first level
            while (intList.Count > 1 && lastId == 0)
            {
                intList.RemoveAt(intList.Count - 1);
                lastId = intList[^1];
            }

            return lastId != 0 ? await GetLocationAreaIdByParentIdAsync(lastId) : await GetAllLocationAreaIdsAsync();
        }

        public async Task<List<int>> GetLocationAreaIdsForAllRegionIdsAsync(List<int> locationItemRegionIds)
        {
            var context = GetContext();
            var query = context.Set<LocationItem>()
                .Where(p => p.Location.LocationLevel == LocationLevel.LocationAreas
                            && p.IsStatus != Constants.DELETE_RECORD
                            && p.IsStatus != Constants.PRE_DELETE_RECORD);

            var results = await query.ToListAsync();

            var locationAreaIds = locationItemRegionIds
                .SelectMany(parentId => results
                    .Where(item =>
                        item.Hierarchy.StartsWith(parentId + ",") ||
                        item.Hierarchy.EndsWith("," + parentId) ||
                        item.Hierarchy.Contains("," + parentId + ",") ||
                        item.Hierarchy.Equals(parentId.ToString()))
                    .Select(item => item.Id))
                .ToList();

            CtxDisposeOrNot(context);
            return locationAreaIds;
        }
    }
}
