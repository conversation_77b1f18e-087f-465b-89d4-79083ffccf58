﻿using Microsoft.EntityFrameworkCore;
using MMS.Core.CoreUTI;
using MMS.Core.dbContext;
using MMS.Core.Entities;
using MMS.Core.Utils;
using MMS.Model.ApiModelResponse;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace MMS.Core.Repository.Impl
{
    public class TerminalBrandMasterRepository : BaseRepository<TerminalBrandMaster>, ITerminalBrandMasterRepository
    {
        public TerminalBrandMasterRepository(DbContextFactory contextFactory) : base(contextFactory)
        {
        }

        public async Task<IList<DeviceBrandsApiModel>> GetAllBrandsForApiAsync()
        {
            var context = GetContext();
            var query = context.Set<TerminalBrandMaster>().AsNoTracking()
                .Where(p => p.IsStatus != Constants.DELETE_RECORD)
                .OrderBy(p => p.OrderIndex)
                .Select(b => new DeviceBrandsApiModel
                {
                    Name = b.<PERSON>ame
                }.MapBaseProperties(b));

            var result = await query.ToListAsync();
            CtxDisposeOrNot(context);
            return result;
        }
    }
}
