﻿using Microsoft.EntityFrameworkCore;
using MMS.Core.CoreUTI;
using MMS.Core.dbContext;
using MMS.Core.Entities;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace MMS.Core.Repository.Impl
{
    public class DeviceCategoriesRepository : BaseRepository<DeviceCategories>, IDeviceCategoriesRepository
    {
        public DeviceCategoriesRepository(DbContextFactory contextFactory) : base(contextFactory)
        {
        }

        public async Task<IList<DeviceCategories>> GetAllAsync()
        {
            var context = GetContext();
            var result = await context.Set<DeviceCategories>()
                .Where(p => p.IsStatus == Constants.NOTCHANGE_RECORD)
                .ToListAsync();

            CtxDisposeOrNot(context);
            return result;
        }
    }
}
