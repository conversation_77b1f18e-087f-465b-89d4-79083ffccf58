using Microsoft.EntityFrameworkCore;
using MMS.Core.CoreUTI;
using MMS.Core.dbContext;
using MMS.Core.Entities;
using MMS.Core.Utils;
using MMS.Model.ApiModelResponse.ApplicationsSetup;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace MMS.Core.Repository.Impl
{
    class ApplicationDeviceMappingRepository : BaseRepository<ApplicationDeviceMapping>, IApplicationDeviceMappingRepository
    {

        public ApplicationDeviceMappingRepository(
            DbContextFactory contextFactory) : base(contextFactory)
        {
        }

        public ApplicationDeviceMapping GetApplicationDeviceMapping(int terminalId, int applicationId)
        {
            var context = GetContext();
            var q = context.ApplicationDeviceMappings.Where(p => p.TerminalMasterId == terminalId && p.ApplicationId == applicationId && p.IsStatus != Constants.DELETE_RECORD && p.IsStatus != Constants.PRE_DELETE_RECORD).OrderByDescending(p => p.Id);

            var applicationDeviceMapping = q.FirstOrDefault();

            CtxDisposeOrNot(context);

            return applicationDeviceMapping;
        }

        public async Task<(int, int)> GetMappingIdAndAppIdByTerminalMasterIdAsync(int terminalMasterId)
        {
            var context = GetContext();
            var query = await context.Set<ApplicationDeviceMapping>().FirstOrDefaultAsync(
                p => p.TerminalMasterId == terminalMasterId && p.IsStatus == Constants.NOTCHANGE_RECORD);

            CtxDisposeOrNot(context);
            if (query is null)
            {
                return (0, 0);
            }

            return (query.Id, query.ApplicationId);
        }

        public async Task<List<int>> FilterUsedVersionIdsByAppAsync(IEnumerable<int> versionMasterIds)
        {
            var context = GetContext();
            var result = context.Set<ApplicationDeviceMapping>().Where(p =>
                p.ApplicationVersionId.HasValue
                && versionMasterIds.Contains(p.ApplicationVersionId.Value)
                && p.IsStatus != Constants.DELETE_RECORD
                && p.IsStatus != Constants.DEACTIVE_RECORD
                && p.IsActive);

            var resultList = await result.Select(p => p.ApplicationVersionId.Value).ToListAsync();
            CtxDisposeOrNot(context);
            return resultList;
        }

        public async Task<List<ApplicationDeviceMapping>> GetInstalledApplications(int terminalMasterId)
        {
            var context = GetContext();
            var result = await context.Set<ApplicationDeviceMapping>().Where(p =>
                p.TerminalMasterId == terminalMasterId
                && p.IsInstalled
                && p.IsStatus != Constants.DELETE_RECORD
                && p.IsStatus != Constants.DEACTIVE_RECORD
                && p.IsActive).Include(p => p.Application).Include(p => p.ApplicationVersion).ToListAsync();

            var numberOfInstalledApp = result.GroupBy(p => p.ApplicationId).Select(gr => new
            {
                ApplicationId = gr.Key,
                Count = gr.Count()
            });

            foreach (var group in numberOfInstalledApp)
            {
                if (group.Count > 1)
                {
                    throw new Exception(string.Format("Existing two Application installed with id {0}", group.ApplicationId));
                }
            }

            CtxDisposeOrNot(context);
            return result;
        }

        public async Task<string> GetVersionByApplicationDeviceMappingId(int applicationDeviceMapping)
        {
            var context = GetContext();
            var result = await context.Set<ApplicationDeviceMapping>().Include(p => p.ApplicationVersion).SingleOrDefaultAsync(p => p.Id == applicationDeviceMapping);
            ArgumentNullException.ThrowIfNull(result);

            CtxDisposeOrNot(context);
            return result.ApplicationVersion.Name;
        }

        public async Task<AppInformationResponse> GetAppNameAndVersionAsync(int appDeviceMappingId)
        {
            var context = GetContext();
            var query = context.Set<ApplicationDeviceMapping>()
                .Where(p => p.Id == appDeviceMappingId)
                .Select(p =>
                    new AppInformationResponse(p.Application.Name, p.ApplicationVersion.VersionNumber, p.IsInstalled)
                    .MapBaseProperties(p));

            var result = await query.FirstOrDefaultAsync();
            CtxDisposeOrNot(context);

            if (result == null)
            {
                throw new KeyNotFoundException($"Application device mapping not found with ID={appDeviceMappingId}");
            }

            return result;
        }

        public async Task<bool> IsInstalledApplication(int applicationDeviceMappingId)
        {
            var context = GetContext();
            var result = await context.Set<ApplicationDeviceMapping>().AnyAsync(p => p.Id == applicationDeviceMappingId && p.IsInstalled);

            CtxDisposeOrNot(context);
            return result;
        }

        public async Task UninstallAppAsync(int terminalMasterId, int registeredAppId)
        {
            using var context = GetContext();

            var installedAppQuery = context.ApplicationDeviceMappings
                .Where(p => p.TerminalMasterId == terminalMasterId
                    && p.ApplicationId == registeredAppId
                    && p.IsInstalled
                    && p.IsStatus != Constants.DELETE_RECORD
                    && p.IsStatus != Constants.PRE_DELETE_RECORD)
                .Select(p => new
                {
                    InstalledApp = p,
                    DeviceSetupUpgrades = context.DeviceSetupUpgrades
                        .Where(d => d.ApplicationDeviceMappingId == p.Id)
                        .ToList()
                });

            var result = await installedAppQuery.SingleOrDefaultAsync();

            if (result == null)
            {
                throw new InvalidOperationException("Installed application not found.");
            }

            // Update the installed app
            result.InstalledApp.SetUninstall();

            // Mark all device setup upgrades as deleted
            if (result.DeviceSetupUpgrades.Count > 0)
            {
                foreach (var upgrade in result.DeviceSetupUpgrades)
                {
                    upgrade.IsStatus = Constants.DELETE_RECORD;
                }
                context.UpdateRange(result.DeviceSetupUpgrades);
            }

            context.Update(result.InstalledApp);

            // Save changes directly
            await context.SaveChangesAsync();
        }
    }
}
