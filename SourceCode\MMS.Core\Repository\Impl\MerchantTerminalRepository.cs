﻿using Microsoft.EntityFrameworkCore;
using MMS.Core.dbContext;
using MMS.Core.Entities;
using MMS.Core.CoreUTI;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using MMS.Core.CoreUTI.Enum;
using System.Threading.Tasks;
using MMS.Core.Entities.Locations;

namespace MMS.Core.Repository.Impl
{
    public class MerchantTerminalRepository : BaseRepository<MerchantTerminal>, IMerchantTerminalRepository
    {
        public MerchantTerminalRepository(DbContextFactory contextFactory) : base(contextFactory)
        {
        }
        public IList<MerchantTerminal> GetMerchantByTerminalId(int skip, int take, int terminalId)
        {
            var context = GetContext();
            var merchants = context.MerchantTerminal.Where(p => p.TerminalId == terminalId && p.IsStatus != Constants.DELETE_RECORD && p.IsStatus != Constants.PRE_DELETE_RECORD)
                .Include(p => p.MerchantMaster).Skip(skip).Take(take);
            var result = merchants.ToList();
            CtxDisposeOrNot(context);
            return result;
        }
        public IList<MerchantTerminal> GetMerchantTerminalsByTerminalId(int terminalId)
        {
            var context = GetContext();
            var result = context.Set<MerchantTerminal>()
                .Where(p => p.IsStatus != Constants.DELETE_RECORD && p.IsStatus != Constants.PRE_DELETE_RECORD && p.TerminalId == terminalId)
                .Include(p => p.StoreForwards)
                .Include(p => p.MerchantMaster)

                //.Include(p => p.SecurityLevels)
                //.Include(p => p.Tips)
                //.Include(p => p.SalesTypes)
                //.Include(p => p.Setups)
                .Include(p => p.CreditCardsAccepted)
                .Include(p => p.DebitCardsAccepted)

                //.Include(p => p.CardLimits)
                .Include(p => p.DccCards)
                .Include(p => p.ManualCards)
                .Include(p => p.Surcharges)
                //.Include(p => p.MerchantCash)

                //.Include(p => p.MerchantCash)
                //.Include(p => p.GiftTenderType)
                //.Include(p => p.LoyalTenderType)

                //.Include(p => p.MerchantReport)
                // .Include(p => p.PaymentModes).ThenInclude(p => p.PaymentModeOption)
                .ToList();

            CtxDisposeOrNot(context);

            return result;
        }

        public MerchantTerminal GetMerchantUsers(int merchantTerminalId)
        {
            var context = GetContext();
            var result = context.Set<MerchantTerminal>()
                .Where(p => p.IsStatus != Constants.DELETE_RECORD && p.IsStatus != Constants.PRE_DELETE_RECORD && p.Id == merchantTerminalId)
                .Include(p => p.MerchantUsers.Where(x => x.IsStatus != Constants.DELETE_RECORD && x.UserTerminal.IsStatus != Constants.DELETE_RECORD))
                .FirstOrDefault();

            CtxDisposeOrNot(context);

            return result;

        }

        public MerchantTerminal GetMerchantFunctionTypes(int merchantTerminalId)
        {
            var context = GetContext();
            var result = context.Set<MerchantTerminal>()
                .Where(p => p.IsStatus != Constants.DELETE_RECORD && p.IsStatus != Constants.PRE_DELETE_RECORD && p.Id == merchantTerminalId)
                .Include(p => p.FunctionTypes.Where(x => x.IsStatus != Constants.DELETE_RECORD))
                .FirstOrDefault();

            CtxDisposeOrNot(context);

            return result;

        }

        public MerchantTerminal GetMerchantTenderTypes(int merchantTerminalId)
        {
            var context = GetContext();
            var result = context.Set<MerchantTerminal>()
                .Where(p => p.IsStatus != Constants.DELETE_RECORD && p.IsStatus != Constants.PRE_DELETE_RECORD && p.Id == merchantTerminalId)
                .Include(p => p.TenderTypes.Where(x => x.IsStatus != Constants.DELETE_RECORD && x.Type != EftPayment.None))
                .FirstOrDefault();

            CtxDisposeOrNot(context);

            return result;

        }

        public MerchantTerminal GetTips(int merchantTerminalId)
        {
            var context = GetContext();
            var result = context.Set<MerchantTerminal>()
                .Where(p => p.IsStatus != Constants.DELETE_RECORD && p.IsStatus != Constants.PRE_DELETE_RECORD && p.Id == merchantTerminalId)
                .Include(p => p.Tips)
                .FirstOrDefault();

            CtxDisposeOrNot(context);

            return result;

        }

        public MerchantTerminal GetPreEmvSurcharge(int merchantTerminalId)
        {
            var context = GetContext();
            var result = context.Set<MerchantTerminal>()
                .Where(p => p.IsStatus != Constants.DELETE_RECORD && p.IsStatus != Constants.PRE_DELETE_RECORD && p.Id == merchantTerminalId)
                .Include(p => p.PreEmvSurcharge)
                .FirstOrDefault();

            CtxDisposeOrNot(context);

            return result;

        }

        public MerchantTerminal GetSalesTypes(int merchantTerminalId)
        {
            var context = GetContext();
            var result = context.Set<MerchantTerminal>()
                .Where(p => p.IsStatus != Constants.DELETE_RECORD && p.IsStatus != Constants.PRE_DELETE_RECORD && p.Id == merchantTerminalId)
                .Include(p => p.SalesTypes)
                .FirstOrDefault();

            CtxDisposeOrNot(context);

            return result;

        }

        public MerchantTerminal GetSetups(int merchantTerminalId)
        {
            var context = GetContext();
            var result = context.Set<MerchantTerminal>()
                .Where(p => p.IsStatus != Constants.DELETE_RECORD && p.IsStatus != Constants.PRE_DELETE_RECORD && p.Id == merchantTerminalId)
                .Include(p => p.Setups)
                .Include(p => p.EmailReceipt)
                .Include(p => p.SmsReceipt)
                .FirstOrDefault();

            CtxDisposeOrNot(context);

            return result;

        }

        public MerchantTerminal GetPaymentTenderTypes(int merchantTerminalId)
        {
            var context = GetContext();
            var result = context.Set<MerchantTerminal>()
                .Where(p => p.IsStatus != Constants.DELETE_RECORD && p.IsStatus != Constants.PRE_DELETE_RECORD && p.Id == merchantTerminalId)
                .Include(p => p.PaymentTenderTypes.Where(p => p.IsStatus != Constants.DELETE_RECORD && p.IsStatus != Constants.PRE_DELETE_RECORD))
                .FirstOrDefault();

            CtxDisposeOrNot(context);

            return result;
        }

        public MerchantTerminal GetMerchantReport(int merchantTerminalId)
        {
            var context = GetContext();
            var result = context.Set<MerchantTerminal>()
                .Where(p => p.IsStatus != Constants.DELETE_RECORD && p.IsStatus != Constants.PRE_DELETE_RECORD && p.Id == merchantTerminalId)
                .Include(p => p.MerchantReport)
                .FirstOrDefault();

            CtxDisposeOrNot(context);

            return result;
        }

        public IList<MerchantTerminal> GetOnlyMerchantTerminalsByTerminalId(int terminalId)
        {
            var context = GetContext();
            var result = context.Set<MerchantTerminal>()
                .Where(p => p.IsStatus != Constants.DELETE_RECORD && p.IsStatus != Constants.PRE_DELETE_RECORD && p.TerminalId == terminalId)
                .Include(p => p.MerchantMaster)
                .ToList();

            CtxDisposeOrNot(context);

            return result;
        }

        public IList<MerchantTerminal> GetMerchantTerminalsIncludeAllImages(int terminalId)
        {
            var context = GetContext();
            var result = context.Set<MerchantTerminal>()
                .Where(p => p.IsStatus != Constants.DELETE_RECORD && p.IsStatus != Constants.PRE_DELETE_RECORD && p.TerminalId == terminalId)
                .Include(p => p.MerchantMaster)
                .Include(p => p.PrintMessage)
                .Include(p => p.PaymentModes)
                .ToList();

            CtxDisposeOrNot(context);

            return result;
        }

        public MerchantTerminal GetPaymentModes(int merchantTerminalId)
        {
            var context = GetContext();
            var result = context.Set<MerchantTerminal>()
                .Where(p => p.IsStatus != Constants.DELETE_RECORD && p.IsStatus != Constants.PRE_DELETE_RECORD && p.Id == merchantTerminalId)
                .Include(p => p.PaymentModes).ThenInclude(p => p.PaymentModeOption)
                .FirstOrDefault();

            CtxDisposeOrNot(context);

            return result;

        }

        public MerchantTerminal GetSurcharges(int merchantTerminalId)
        {
            var context = GetContext();
            var result = context.Set<MerchantTerminal>().Where(p => p.IsStatus != Constants.DELETE_RECORD && p.IsStatus != Constants.PRE_DELETE_RECORD && p.Id == merchantTerminalId)
                .Include(p => p.SurchargeRules)
                .FirstOrDefault();

            CtxDisposeOrNot(context);

            return result;
        }

        public MerchantTerminal GetMerchantCash(int merchantTerminalId)
        {
            var context = GetContext();
            var result = context.Set<MerchantTerminal>()
                .Where(p => p.IsStatus != Constants.DELETE_RECORD && p.IsStatus != Constants.PRE_DELETE_RECORD && p.Id == merchantTerminalId)
                .Include(p => p.MerchantCash)
                .FirstOrDefault();

            CtxDisposeOrNot(context);

            return result;

        }

        public MerchantTerminal GetMOTOCard(int merchantTerminalId)
        {
            var context = GetContext();
            var result = context.Set<MerchantTerminal>().Where(p => p.IsStatus != Constants.DELETE_RECORD && p.IsStatus != Constants.PRE_DELETE_RECORD && p.Id == merchantTerminalId)
                .Include(p => p.MOTOCards)
                .FirstOrDefault();

            CtxDisposeOrNot(context);

            return result;
        }

        public MerchantTerminal GetCardLimits(int merchantTerminalId)
        {
            var context = GetContext();
            var result = context.Set<MerchantTerminal>()
                .Where(p => p.IsStatus != Constants.DELETE_RECORD && p.IsStatus != Constants.PRE_DELETE_RECORD && p.Id == merchantTerminalId)
                .Include(p => p.CardLimits)
                .FirstOrDefault();

            CtxDisposeOrNot(context);

            return result;
        }

        public MerchantTerminal GetSecurityLevels(int merchantTerminalId)
        {
            var context = GetContext();
            var result = context.Set<MerchantTerminal>()
                .Where(p => p.IsStatus != Constants.DELETE_RECORD && p.IsStatus != Constants.PRE_DELETE_RECORD && p.Id == merchantTerminalId)
                .Include(p => p.SecurityLevels)
                .FirstOrDefault();

            CtxDisposeOrNot(context);

            return result;
        }

        public MerchantTerminal GetEOVMerchantLimit(int merchantTerminalId)
        {
            var context = GetContext();
            var result = context.Set<MerchantTerminal>().Where(p => p.IsStatus != Constants.DELETE_RECORD && p.IsStatus != Constants.PRE_DELETE_RECORD && p.Id == merchantTerminalId)
                .Include(p => p.EOVCardLimits)
                .FirstOrDefault();

            CtxDisposeOrNot(context);

            return result;
        }

        public MerchantTerminal GetCashOutFees(int merchantTerminalId)
        {
            var context = GetContext();
            var result = context.Set<MerchantTerminal>()
                .Where(p => p.IsStatus != Constants.DELETE_RECORD && p.IsStatus != Constants.PRE_DELETE_RECORD && p.Id == merchantTerminalId)
                .Include(p => p.CashOutFees)
                .FirstOrDefault();

            CtxDisposeOrNot(context);

            return result;
        }

        public T GetNaviProperty<T>(int id, Expression<Func<MerchantTerminal, object>> select, params Expression<Func<MerchantTerminal, object>>[] includes) where T : class
        {
            T result = default(T);
            var context = GetContext();
            lock (context)
            {
                var query = context.Set<MerchantTerminal>().Where(p => p.Id == id);
                query = query.Include(select);

                foreach (var item in includes)
                {
                    query = query.Include(item);
                }

                result = query.Select(select) as T;

            }
            CtxDisposeOrNot(context);

            return result;
        }

        public MerchantTerminal GetEOVMerchantCreditCard(int merchantTerminalId)
        {
            var context = GetContext();
            var result = context.Set<MerchantTerminal>()
                .Where(p => p.IsStatus != Constants.DELETE_RECORD && p.IsStatus != Constants.PRE_DELETE_RECORD && p.Id == merchantTerminalId)
                .Include(p => p.EOVMerchantCreditCard)
                .FirstOrDefault();

            CtxDisposeOrNot(context);

            return result;

        }

        public MerchantTerminal GetEOVMerchantDebitCard(int merchantTerminalId)
        {
            var context = GetContext();
            var result = context.Set<MerchantTerminal>()
                .Where(p => p.IsStatus != Constants.DELETE_RECORD && p.IsStatus != Constants.PRE_DELETE_RECORD && p.Id == merchantTerminalId)
                .Include(p => p.EOVMerchantDebitCard)
                .FirstOrDefault();

            CtxDisposeOrNot(context);

            return result;

        }

        public IList<T> GetNaviPropertyList<T>(int id, Expression<Func<MerchantTerminal, object>> select, params Expression<Func<MerchantTerminal, object>>[] includes) where T : class
        {
            IList<T> result = new List<T>();
            var context = GetContext();
            lock (context)
            {
                var query = context.Set<MerchantTerminal>().Where(p => p.Id == id);
                query = query.Include(select);

                foreach (var item in includes)
                {
                    query = query.Include(item);
                }

                result = (query.Select(select) as IList<T>) ?? new List<T>();

            }
            CtxDisposeOrNot(context);

            return result;
        }

        public async Task<IList<MerchantTerminal>> GetMerchantTerminalsByTerminalIdAsync(int terminalMasterId)
        {
            return await ScopedEntities
                .Where(mt => mt.TerminalId == terminalMasterId && mt.IsStatus == Constants.NOTCHANGE_RECORD && mt.IsActive)
                .ToListAsync();
        }

        public async Task<IList<MerchantTerminal>> GetMerchantTerminalsByTerminalIds(IList<int> terminalIds)
        {
            if (terminalIds == null || !terminalIds.Any())
            {
                return new List<MerchantTerminal>();
            }

            return await ScopedEntities
                .Where(mt => mt.TerminalId.HasValue
                    && terminalIds.Contains(mt.TerminalId.Value)
                    && mt.IsStatus == Constants.NOTCHANGE_RECORD
                    && mt.IsActive)
                .ToListAsync();
        }

        public async Task<TerminalMaster> GetTerminalMasterIncludeLocationAsync(int terminalMasterId)
        {
            return await ScopedMMSContext.Set<TerminalMaster>()
                .Include(tm => tm.LocationArea)
                    .ThenInclude(lo => lo.Parent)
                .FirstOrDefaultAsync(
                    tm =>
                        tm.Id == terminalMasterId &&
                        tm.IsStatus != Constants.PRE_DELETE_RECORD && tm.IsStatus != Constants.DELETE_RECORD && tm.IsActive);
        }

        public async Task<MerchantMaster> GetMerchantForSingleModeAsync(int parentLocationId)
        {
            var merchantLocation = await ScopedMMSContext.Set<MerchantLocations>()
                .Include(ml => ml.MerchantMaster)
                .FirstOrDefaultAsync(ml => ml.LocationItemId == parentLocationId && ml.IsStatus == Constants.NOTCHANGE_RECORD);

            return merchantLocation?.MerchantMaster;
        }

        public async Task<IList<MerchantMaster>> GetMerchantsForMultiModeAsync(int locationAreaId)
        {
            var merchantLocationList = await ScopedMMSContext.Set<MerchantLocations>()
                .Include(ml => ml.MerchantMaster)
                .Where(ml => ml.LocationItemId == locationAreaId && ml.IsStatus == Constants.NOTCHANGE_RECORD && ml.IsActive)
                .ToListAsync();

            return merchantLocationList.Select(ml => ml.MerchantMaster).ToList();
        }

        public async Task<(int, bool)> IsMultiMerchantAsync(int locationAreaId)
        {
            var locationArea = await ScopedMMSContext.Set<LocationItem>()
                .Include(l => l.Parent)
                .FirstOrDefaultAsync(li => li.Id == locationAreaId && li.IsStatus == Constants.NOTCHANGE_RECORD);

            if (locationArea is null) return (0, false);

            var location = await ScopedMMSContext.Set<Location>().FirstOrDefaultAsync(l => l.Id == locationArea.Parent.LocationId);

            if (location is null) return (0, false);

            if (location.LocationLevel == LocationLevel.Locations)
            {
                var merchantLocation = await ScopedMMSContext.Set<MerchantLocations>()
                    .Include(ml => ml.MerchantMaster)
                    .FirstOrDefaultAsync(ml =>
                        ml.LocationItemId == locationArea.Parent.Id &&
                        ml.IsStatus == Constants.NOTCHANGE_RECORD);

                // merchant master is deactivated
                if (merchantLocation is not null && merchantLocation.MerchantMaster.IsActive)
                    return (merchantLocation.MerchantMasterId, locationArea.Parent.MultiMerchantLocation);

                return (0, locationArea.Parent.MultiMerchantLocation);
            }

            return (0, false);
        }

        public async Task<IList<MerchantTerminal>> GetMerchantTemplatesByTerminalIdAsync(int terminalMasterId)
        {
            return await ScopedMMSContext.Set<MerchantTerminal>()
                .Include(mt => mt.MerchantMaster)
                .Where(o => o.TerminalId == terminalMasterId && o.IsStatus != Constants.DELETE_RECORD && o.MerchantMaster.IsStatus != Constants.DELETE_RECORD)
                .ToListAsync();
        }
    }
}
