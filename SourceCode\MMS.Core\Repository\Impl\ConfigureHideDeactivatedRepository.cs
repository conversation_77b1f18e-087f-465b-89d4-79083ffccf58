﻿using System.Linq;
using MMS.Core.dbContext;
using MMS.Core.Entities;

namespace MMS.Core.Repository.Impl
{
    public class ConfigureHideDeactivatedRepository : BaseRepository<ConfigureHideDeactivated>, IConfigureHideDeactivatedRepository
    {
        public ConfigureHideDeactivatedRepository(DbContextFactory contextFactory) : base(contextFactory)
        {
        }

        public ConfigureHideDeactivated GetFirst()
        {
            var context = GetContext();
            var firstConfig = context.Set<ConfigureHideDeactivated>().FirstOrDefault();

            if (firstConfig == null)
            {
                firstConfig = new ConfigureHideDeactivated();
                Insert(firstConfig);
            }

            CtxDisposeOrNot(context);
            return firstConfig;
        }

        public void UpdateConfig(ConfigureHideDeactivated config)
        {
            Update(config);
        }
    }
}
