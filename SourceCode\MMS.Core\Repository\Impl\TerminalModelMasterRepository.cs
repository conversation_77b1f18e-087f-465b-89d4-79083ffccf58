﻿using Microsoft.EntityFrameworkCore;
using MMS.Core.CoreUTI;
using MMS.Core.dbContext;
using MMS.Core.Entities;
using MMS.Core.Utils;
using MMS.Model.ApiModelResponse;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace MMS.Core.Repository.Impl
{
    public class TerminalModelMasterRepository : BaseRepository<TerminalModelMaster>, ITerminalModelMasterRepository
    {
        public TerminalModelMasterRepository(DbContextFactory contextFactory) : base(contextFactory)
        {
        }

        public async Task<IList<DeviceModelsApiModel>> GetModelsByBrandForApiAsync(List<int> brandIds)
        {
            var context = GetContext();
            var query = context.Set<TerminalModelMaster>().AsNoTracking()
                .Where(p => p.IsStatus != Constants.DELETE_RECORD
                         && brandIds.Contains(p.TerminalBrandId))
                .OrderBy(p => p.OrderIndex)
                .Select(b => new DeviceModelsApiModel
                {
                    Name = b.TerminalModelName
                }.MapBaseProperties(b));

            var result = await query.ToListAsync();
            CtxDisposeOrNot(context);
            return result;
        }
    }
}
