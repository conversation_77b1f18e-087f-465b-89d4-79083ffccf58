﻿using Microsoft.EntityFrameworkCore;
using MMS.Core.CoreUTI;
using MMS.Core.CoreUTI.Enum;
using MMS.Core.dbContext;
using MMS.Core.Entities;
using MMS.Core.Entities.Locations;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;

namespace MMS.Core.Repository.Impl
{
    public class MerchantLocationRepository : BaseRepository<MerchantLocations>, IMerchantLocationRepository
    {
        public MerchantLocationRepository(DbContextFactory contextFactory) : base(contextFactory)
        {
        }

        public async Task<IList<MerchantLocations>> GetAllMerchantLocationAsync(int locationItemId)
        {
            var context = GetContext();

            var allMerchantLocation = await context.Set<MerchantLocations>()
                .Where(ml => ml.LocationItemId == locationItemId && ml.IsStatus == Constants.NOTCHANGE_RECORD)
                .ToListAsync();

            CtxDisposeOrNot(context);

            return allMerchantLocation;
        }

        public async Task<MerchantLocations> GetMerchantLocationWithOptionalIncludeAsync(int locationItemId, params Expression<Func<MerchantLocations, object>>[] includes)
        {
            var context = GetContext();

            IQueryable<MerchantLocations> merchantLocation = context.Set<MerchantLocations>().AsQueryable();

            if (includes != null && includes.Length != 0)
            {
                foreach (var include in includes)
                {
                    merchantLocation = merchantLocation.Include(include);
                }
            }

            var result = await merchantLocation.FirstOrDefaultAsync(ml =>
                ml.LocationItemId == locationItemId && ml.IsStatus == Constants.NOTCHANGE_RECORD);

            CtxDisposeOrNot(context);

            return result;
        }

        public async Task<List<MerchantLocations>> GetMerchantLocationListByMerchantMasterIdAsync(int merchantMasterId)
        {
            var context = GetContext();
            var query = await context.Set<MerchantLocations>()
                .Where(p => p.MerchantMasterId == merchantMasterId && p.IsStatus == Constants.NOTCHANGE_RECORD)
                .ToListAsync();

            CtxDisposeOrNot(context);
            return query;
        }

        public async Task<(LocationLevel, string, int)> GetLocationItemInforAsync(int locationItemId)
        {
            var context = GetContext();
            var query = await context.Set<LocationItem>()
                .Include(p => p.Location)
                .FirstOrDefaultAsync(p => p.Id == locationItemId && p.IsStatus == Constants.NOTCHANGE_RECORD);

            CtxDisposeOrNot(context);
            if (query is null) return (LocationLevel.Master, string.Empty, 0);

            return (query.Location.LocationLevel, query.GetLocationItemName, query.ParentId ?? 0);
        }

        public async Task<MerchantLocations> GetMerchantLocationIncludeMerchantMasterAsync(int locationItemId)
        {
            var context = GetContext();

            var merchantLocation = await context.Set<MerchantLocations>()
                .Include(ml => ml.MerchantMaster)
                .FirstOrDefaultAsync(ml =>
                    ml.LocationItemId == locationItemId && ml.IsStatus == Constants.NOTCHANGE_RECORD);

            CtxDisposeOrNot(context);

            return merchantLocation;
        }

        public async Task<IList<MerchantLocations>> GetMerchantLocationListByLocationAreaIdIncludeMerchantMasterAsync(int locationAreaId)
        {
            var context = GetContext();

            var merchantLocationList = await context.Set<MerchantLocations>()
                .Where(ml => ml.LocationItemId == locationAreaId && ml.IsStatus == Constants.NOTCHANGE_RECORD)
                .Include(ml => ml.MerchantMaster)
                .ToListAsync();

            CtxDisposeOrNot(context);

            return merchantLocationList;
        }

        public async Task<List<MerchantMaster>> GetMerchantLocationListSelectedByOtherLocationAsync(int locationId)
        {
            var context = GetContext();
            var locationItem = await context.Set<LocationItem>()
                .Include(p => p.Location)
                .FirstOrDefaultAsync(p => p.Id == locationId && p.IsStatus == Constants.NOTCHANGE_RECORD);

            if (locationItem is null)
            {
                CtxDisposeOrNot(context);
                return [];
            }

            if (locationItem.Location is null)
            {
                CtxDisposeOrNot(context);
                return [];
            }

            if (locationItem.Location.LocationLevel == CoreUTI.Enum.LocationLevel.LocationAreas)
                locationId = locationItem.ParentId ?? 0;

            var locationAreaIdsList = context.Set<LocationItem>()
                    .Where(p => p.ParentId == locationId)
                    .Select(p => p.Id);

            var merchantLocationSelectedByOtherLocationList = await context.Set<MerchantLocations>()
                    .Where(p =>
                        p.LocationItemId != locationId && !locationAreaIdsList.Contains(p.LocationItemId) &&
                        p.IsStatus == Constants.NOTCHANGE_RECORD)
                    .Select(p => p.MerchantMaster)
                    .ToListAsync();

            CtxDisposeOrNot(context);
            return merchantLocationSelectedByOtherLocationList;
        }
    }
}
