﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using MMS.Core.ConfigOptions;
using MMS.Core.CoreUTI;
using MMS.Core.CoreUTI.Enum;
using MMS.Core.dbContext;
using MMS.Core.Entities;
using MMS.Core.Models.Converters;
using MMS.Core.Utils;
using MMS.Infrastructure.Commons;
using MMS.Model.ApiModelRequest;
using MMS.Model.ApiModelResponse;
using MMS.Model.ApiModelResponse.Company;

namespace MMS.Core.Repository.Impl
{
    public class CompanyDetailRepository : BaseRepository<CompanyDetails>, ICompanyDetailRepository
    {
        private readonly IOptions<TerminalSourceOptions> _terminalSourceOptions;

        public CompanyDetailRepository(
            DbContextFactory contextFactory,
            IOptions<TerminalSourceOptions> terminalSourceOptions = null) : base(contextFactory)
        {
            _terminalSourceOptions = terminalSourceOptions;
        }

        public async Task<List<CompanyDetails>> GetAllMasterCompanyAsync()
        {
            var context = GetContext();
            var query = context.CompanyDetails.AsNoTracking()
                               .Where(p => !p.ParentId.HasValue
                                        && p.IsStatus != Constants.DELETE_RECORD)
                               .OrderBy(p => p.OrderIndex);

            return await query.ToListAsync();
        }

        public async Task<List<CompanyDetails>> GetAllCompanyExcludingCurrentAsync(List<int> excludingCompanyIds)
        {
            var context = GetContext();
            var query = context.CompanyDetails.AsNoTracking()
                .Where(p => p.IsStatus != Constants.DELETE_RECORD
                    && (!p.ParentId.HasValue ||
                        (p.Parent.IsActive && p.SubsidiaryCompanyMappings.Any(s => s.IsActive))));

            if (excludingCompanyIds != null)
                query = query.Where(p => !excludingCompanyIds.Contains(p.Id))
                    .Include(p => p.Parent)
                    .Include(p => p.SubsidiaryCompanyMappings);

            return await query.OrderBy(p => p.OrderIndex).ToListAsync();
        }

        public async Task<IList<CompanyDetails>> GetCompaniesMasterAsync(int skip, int take, string searchKey)
        {
            var context = GetContext().Set<CompanyDetails>();
            var queryable = context.Where(p => p.ParentId == null
                && p.IsStatus != Constants.DELETE_RECORD
                && p.IsStatus != Constants.PRE_DELETE_RECORD);

            if (!string.IsNullOrWhiteSpace(searchKey))
                queryable = queryable.Where(p => p.Name.Contains(searchKey));

            return await queryable.Take(take).Skip(skip).ToListAsync();
        }

        public async Task<PagingResponse<CompanyListResponse>> GetCompaniesMasterAsync(string searchKey, PagingParameter pagingParameter)
        {
            var context = GetContext().Set<CompanyDetails>();
            var query = context.AsNoTracking().Where(p => p.ParentId == null
                && p.IsStatus != Constants.DELETE_RECORD
                && p.IsStatus != Constants.PRE_DELETE_RECORD);

            // Use the provided terminal source if specified, otherwise use the configured source
            var sourceToUse = _terminalSourceOptions?.Value?.GetTerminalSource();

            if (!sourceToUse.HasValue || sourceToUse.Value == TerminalSources.None) throw new InvalidOperationException("Terminal source is not configured");

            // Filter by terminal source if available
            if (sourceToUse.Value == TerminalSources.Linkly)
            {
                query = query.Where(p => !string.IsNullOrWhiteSpace(p.LinklyId));
            }
            else
            {
                query = query.Where(p => string.IsNullOrWhiteSpace(p.LinklyId));
            }

            query = SearchUtility.ApplySearch(query, searchKey, q => q.CompanyTradingName);

            var companies = from q in query
                            select CompanyModelConvert.ToCompanyItemModel(q);

            return await GetPagingResponseAsync(companies, pagingParameter.PageNumber, pagingParameter.PageSize);
        }

        public async Task<IList<CompanyDetails>> GetSubCompaniesByParentIdAsync(int parentId)
        {
            var context = GetContext().Set<CompanyDetails>();
            var result = await context.Where(p => p.ParentId == parentId
                                                  && p.IsStatus != Constants.DELETE_RECORD
                                                  && p.IsStatus != Constants.PRE_DELETE_RECORD).ToListAsync();

            return result;
        }

        public async Task<HashSet<int>> GetDeletedCompanyIdsInHierarchy(List<int> ids)
        {
            var context = GetContext();
            return new HashSet<int>(
                await context.CompanyDetails
                    .Where(p => ids.Contains(p.Id) && p.IsStatus == Constants.DELETE_RECORD)
                    .Select(p => p.Id)
                    .ToListAsync()
            );
        }

        public async Task<CompanyInfoResponse> GetCompanyInfoByIdAsync(int id)
        {
            var context = GetContext();
            var query = context.CompanyDetails
                .AsNoTracking()
                .Where(p => p.Id == id
                            && p.IsStatus != Constants.DELETE_RECORD
                            && p.IsStatus != Constants.PRE_DELETE_RECORD);

            var companyInfo = from q in query
                              select CompanyModelConvert.ToCompanyInfoModel(q);

            return await companyInfo.FirstOrDefaultAsync();
        }

        public async Task<PagingResponse<DataItemResponse>> GetSourceForSelectUserAsync(int companyId, List<int> selectedIds, string searchKey, SelectedFilterType filterType, PagingParameter pagingParameter)
        {
            var context = GetContext();
            var query = context.UserMaster.AsNoTracking()
                .Where(p => p.IsStatus != Constants.DELETE_RECORD
                            && p.IsStatus != Constants.PRE_DELETE_RECORD);

            if (filterType == SelectedFilterType.Selected)
            {
                query = query.Where(p => selectedIds.Contains(p.Id));
            }
            else if (filterType == SelectedFilterType.NotSelected)
            {
                query = query.Where(p => !selectedIds.Contains(p.Id));
            }

            query = SearchUtility.ApplySearch(query, searchKey, q => q.FirstName, q => q.MiddleName, q => q.SurName);
            var companies = from q in query
                            select new DataItemResponse(q.Id, q.Name, q.IsActive);

            return await GetPagingResponseAsync(companies, pagingParameter.PageNumber, pagingParameter.PageSize);
        }

        public async Task<IList<CompanyDetails>> GetLinklyCompaniesAsync()
        {
            var context = GetContext();
            var query = from b in context.CompanyDetails.AsNoTracking()
                        where b.IsStatus != Constants.DELETE_RECORD
                        && !string.IsNullOrWhiteSpace(b.LinklyId)
                        select b;
            var result = await query.ToListAsync();

            return result;
        }
    }
}
