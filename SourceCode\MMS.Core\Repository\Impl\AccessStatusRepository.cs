﻿using System;
using Microsoft.EntityFrameworkCore;
using MMS.Core.CoreUTI;
using MMS.Core.dbContext;
using MMS.Core.Entities;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace MMS.Core.Repository.Impl
{
    public class AccessStatusRepository : BaseRepository<AccessStatus>, IAccessStatusRepository
    {
        public AccessStatusRepository(DbContextFactory contextFactory) : base(contextFactory)
        {
        }

        public async Task<List<AccessStatus>> GetAllAccessStatusesExceptSpecifiedAsync(int accessStatusId)
        {
            var context = GetContext();
            var statuses = await context.Set<AccessStatus>().Where(p => p.Id != accessStatusId && p.IsStatus == Constants.NOTCHANGE_RECORD && p.IsActive).ToListAsync();
            CtxDisposeOrNot(context);
            return statuses;
        }

        public async Task<List<AccessStatus>> GetAccessStatusWithSameCompanyAsync(IEnumerable<int> accessStatusIds, int companyId, int? groupId)
        {
            var context = GetContext();
            var query = context.Set<CloudSecurityAccessMapping>()
                .Where(p => p.SecurityTemplateId.HasValue
                    && accessStatusIds.Contains(p.SecurityTemplateId.Value)
                    && p.CompanyId == companyId
                    && p.IsStatus == Constants.NOTCHANGE_RECORD);

            query = groupId != null
                ? query.Where(p => p.GroupId == groupId)
                : query.Where(p => p.GroupId == null);

            var result = await query.Include(p => p.SecurityTemplate)
                .Select(p => p.SecurityTemplate)
                .ToListAsync();

            CtxDisposeOrNot(context);
            return result;
        }
    }
}
