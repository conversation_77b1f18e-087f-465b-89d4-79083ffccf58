using Microsoft.EntityFrameworkCore;
using MMS.Core.dbContext;
using MMS.Core.Entities;
using MMS.Core.CoreUTI;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Linq.Expressions;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;
using MMS.Core.Entities.Locations;
using Microsoft.EntityFrameworkCore.Diagnostics;
using MMS.Model.ApiModelResponse;
using MMS.Model.Common;
using MMS.Core.Utils;
using MMS.Model.ApiModelRequest;
using MMS.Infrastructure.Commons;

namespace MMS.Core.Repository.Impl
{
    public class MerchantMasterRepository : BaseRepository<MerchantMaster>, IMerchantMasterRepository
    {
        public MerchantMasterRepository(
            DbContextFactory contextFactory
        ) : base(contextFactory)
        {
        }
        /// <summary>
        /// </summary>
        /// <param name="id"></param>
        /// <param name="isIncludeList"></param>
        /// <param name="includes"></param>
        /// <returns></returns>
        public MerchantMaster GetByIdWithIncludes(int id, bool isIncludeList, params Expression<Func<MerchantMaster, object>>[] includes)
        {
            var context = GetContext();
            var query = context.Set<MerchantMaster>().Where(p=>p.Id == id).Include(p=> p.CompanyDetail);
            if (isIncludeList)
            {
                query.Include(p => p.MerchantTerminals).ThenInclude(p => p.Terminal);
            }
            foreach (var item in includes)
            {
                query.Include(item);
            }
            var result = query.FirstOrDefault();
            CtxDisposeOrNot(context);

            return result;
        }

        public async Task<(IList<MerchantMaster> MerchantMasters, bool MultiMerchantLocation)> GetSourceForDeviceSelectMerchantAsync(int terminalMasterId)
        {
            var context = GetContext();

            // First, retrieve the MultiMerchantLocation flag
            var multiMerchantLocation = await (from terminalMaster in context.Set<TerminalMaster>()
                                               where terminalMaster.Id == terminalMasterId
                                               select terminalMaster.LocationArea.Parent.MultiMerchantLocation)
                                          .FirstOrDefaultAsync();

            // Now, based on the flag, retrieve the MerchantMasters
            IQueryable<MerchantMaster> query;

            if (multiMerchantLocation)
            {
                query = from terminalMaster in context.Set<TerminalMaster>()
                        where terminalMaster.Id == terminalMasterId
                        from merchantLocation in terminalMaster.LocationArea.MerchantLocations
                        where merchantLocation.IsStatus == Constants.NOTCHANGE_RECORD
                        select merchantLocation.MerchantMaster;
            }
            else
            {
                query = from terminalMaster in context.Set<TerminalMaster>()
                        where terminalMaster.Id == terminalMasterId
                        from merchantLocation in terminalMaster.LocationArea.Parent.MerchantLocations
                        where merchantLocation.IsStatus == Constants.NOTCHANGE_RECORD
                        select merchantLocation.MerchantMaster;
            }

            var merchantMasters = await query.ToListAsync();
            CtxDisposeOrNot(context);//Clean context (incase using session context concept).

            return (merchantMasters, multiMerchantLocation);
        }

        public async Task<(PagingResponse<SelectListItemModel> Merchants, bool MultiMerchantLocation)> GetSourceForSelectMerchantAsync(int terminalMasterId, IList<int> selectedIds, string searchKey, SelectedFilterType filter, PagingParameter pagingParameter)
        {
            var context = GetContext();

            // First, retrieve the MultiMerchantLocation flag
            var multiMerchantLocation = await (from terminalMaster in context.Set<TerminalMaster>()
                                               where terminalMaster.Id == terminalMasterId
                                               select terminalMaster.LocationArea.Parent.MultiMerchantLocation)
                                          .FirstOrDefaultAsync();

            // Now, based on the flag, retrieve the MerchantMasters
            IQueryable<MerchantMaster> query;

            if (multiMerchantLocation)
            {
                query = from terminalMaster in context.Set<TerminalMaster>()
                        where terminalMaster.Id == terminalMasterId
                        from merchantLocation in terminalMaster.LocationArea.MerchantLocations
                        where merchantLocation.IsStatus == Constants.NOTCHANGE_RECORD
                        select merchantLocation.MerchantMaster;
            }
            else
            {
                query = from terminalMaster in context.Set<TerminalMaster>()
                        where terminalMaster.Id == terminalMasterId
                        from merchantLocation in terminalMaster.LocationArea.Parent.MerchantLocations
                        where merchantLocation.IsStatus == Constants.NOTCHANGE_RECORD
                        select merchantLocation.MerchantMaster;
            }

            if (filter == SelectedFilterType.Selected)
            {
                query = query.Where(q => selectedIds.Contains(q.Id));
            }
            else if (filter == SelectedFilterType.NotSelected)
            {
                query = query.Where(q => !selectedIds.Contains(q.Id));
            }

            // Apply search filter using SearchUtility
            query = SearchUtility.ApplySearch(query, searchKey, q => q.lszMerchantName);

            // Map to SelectListItem after filtering
            var merchants = from q in query
                            select new SelectListItemModel
                            {
                                Name = q.Name,
                                IsSelected = selectedIds.Contains(q.Id)
                            }.MapBaseProperties(q);

            return (await GetPagingResponseAsync(merchants, pagingParameter.PageNumber, pagingParameter.PageSize), multiMerchantLocation);
        }

        public async Task<IList<MerchantMaster>> GetSelectMerchantByLocationItemAsync(int locationAreaId)
        {
            var context = GetContext();

            var query = from merchantLocation in context.Set<MerchantLocations>()
                        where merchantLocation.LocationItemId == locationAreaId
                        where merchantLocation.IsActive && merchantLocation.IsStatus == Constants.NOTCHANGE_RECORD
                        where merchantLocation.MerchantMaster.IsActive && merchantLocation.MerchantMaster.IsStatus == Constants.NOTCHANGE_RECORD
                        select merchantLocation.MerchantMaster;

            var result = await query.ToListAsync();
            CtxDisposeOrNot(context);//Clean context (incase using session context concept).

            return result;
        }

        public async Task<IList<MerchantMaster>> GetMerchantMasterListWithNotNullMerchantCompanyIdAsync()
        {
            var context = GetContext();

            var merchantMasterList = await context.Set<MerchantMaster>()
                .Where(mm => mm.MerchantCompanyId != null && mm.IsActive && mm.IsStatus == Constants.NOTCHANGE_RECORD)
                .OrderBy(mm => mm.lszMerchantName)
                .ToListAsync();

            CtxDisposeOrNot(context);

            return merchantMasterList;
        }

        public async Task<IList<MerchantMaster>> GetLinklyMerchantMastersAsync()
        {
            var context = GetContext();

            var query = from m in context.Set<MerchantMaster>().AsNoTracking()
                        where m.IsStatus != Constants.DELETE_RECORD
                        && !string.IsNullOrWhiteSpace(m.LinklyId)
                        select m;

            var result = await query.ToListAsync();

            return result;
        }
    }
}
