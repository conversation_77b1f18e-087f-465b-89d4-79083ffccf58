﻿using System;
using Microsoft.EntityFrameworkCore;
using MMS.Core.dbContext;
using MMS.Core.Entities;
using MMS.Core.CoreUTI;
using System.Linq;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace MMS.Core.Repository.Impl
{
    public class ActionOfFunctionRepo : BaseRepository<ActionOfFunction>, IActionOfFunctionRepo
    {
        public ActionOfFunctionRepo(DbContextFactory contextFactory) : base(contextFactory)
        {
        }

        public IList<ActionOfFunction> GetFunctionByRole(int accessStatusId)
        {
            var context = GetContext();

            var query = context.ActionOfFunction.Where(p => p.AccessStatusId == accessStatusId && p.CompanyId == null && p.UserId == null && p.IsStatus != Constants.DELETE_RECORD && p.IsStatus != Constants.PRE_DELETE_RECORD);

            var actionOfFunctions = query.ToList();

            CtxDisposeOrNot(context);

            return actionOfFunctions;
        }

        public IList<ActionOfFunction> GetFunctionByRole(int accessStatusId, int funcParentId)
        {
            var context = GetContext();

            var query = context.ActionOfFunction.Where(p => p.AccessStatusId == accessStatusId && p.FunctionParent == funcParentId && p.CompanyId == null && p.UserId == null && p.IsStatus != Constants.DELETE_RECORD && p.IsStatus != Constants.PRE_DELETE_RECORD);

            var actionOfFunctions = query.ToList();

            CtxDisposeOrNot(context);

            return actionOfFunctions;
        }

        public IList<ActionOfFunction> GetFunctionByRoleCompanyUser(int accessStatusId, int companyId, int userId)
        {
            var context = GetContext();

            var query = context.ActionOfFunction.Where(p => p.AccessStatusId == accessStatusId && p.CompanyId == companyId && p.UserId == userId && p.IsStatus != Constants.DELETE_RECORD && p.IsStatus != Constants.PRE_DELETE_RECORD);

            var actionOfFunctions = query.ToList();

            CtxDisposeOrNot(context);

            return actionOfFunctions;
        }

        public IList<ActionOfFunction> GetLevelAccess(IList<int?> accessStatusIds)
        {
            var context = GetContext();

            var query = context.ActionOfFunction.Where(p => p.CompanyId == null && p.UserId == null && p.IsStatus != Constants.DELETE_RECORD && p.IsStatus != Constants.PRE_DELETE_RECORD);
            query = query.Where(p => accessStatusIds.Contains(p.AccessStatusId));

            var actionOfFunctions = query.ToList();

            CtxDisposeOrNot(context);

            return actionOfFunctions;
        }

        public IList<ActionOfFunction> GetLevelAccessCompanyUser(IList<int?> accessStatusIds, int companyId, int userId)
        {
            var context = GetContext();

            var query = context.ActionOfFunction.Where(p => p.CompanyId == companyId && p.UserId == userId && p.IsStatus != Constants.DELETE_RECORD && p.IsStatus != Constants.PRE_DELETE_RECORD);

            var actionOfFunctions = query.ToList();

            actionOfFunctions = actionOfFunctions.Where(p => accessStatusIds.Contains(p.AccessStatusId)).ToList();

            CtxDisposeOrNot(context);

            return actionOfFunctions;
        }

        public async Task<List<ActionOfFunction>> GetLevelAccessWithCompanyIdAsync(IList<int?> accessStatusIds, int companyId)
        {
            var context = GetContext();

            var query = await context.ActionOfFunction
                .Where(p =>
                    p.CompanyId == companyId && p.UserId == null &&
                    accessStatusIds.Contains(p.AccessStatusId) &&
                    p.IsStatus != Constants.PRE_DELETE_RECORD && p.IsStatus != Constants.DELETE_RECORD)
                .ToListAsync();

            CtxDisposeOrNot(context);

            return query;
        }

        public async Task<CloudSecurityAccessMapping> GetSecurityWithCompanyAsync(int accessId, int companyId, int? groupId)
        {
            var query = ScopedMMSContext.Set<CloudSecurityAccessMapping>()
                .Where(p =>
                    p.SecurityTemplateId == accessId
                    && p.CompanyId == companyId
                    && p.IsStatus == Constants.NOTCHANGE_RECORD);

            var result = groupId != null
                ? await query.FirstOrDefaultAsync(p => p.GroupId == groupId)
                : await query.FirstOrDefaultAsync(p => p.GroupId == null);

            return result;
        }

        public async Task<List<CloudSecurityAccessMapping>> GetMappedAccessStatusByTemplateIdAsync(int templateAccessId, int companyId, int? groupId)
        {
            var query = ScopedMMSContext.Set<CloudSecurityAccessMapping>()
                .Where(p =>
                    p.TemplateSecurityLinkingId.HasValue && p.TemplateSecurityLinkingId == templateAccessId &&
                    p.CompanyId == companyId &&
                    p.IsStatus == Constants.NOTCHANGE_RECORD);

            query = groupId != null
                ? query.Where(p => p.GroupId == groupId)
                : query.Where(p => p.GroupId == null);

            return await query.ToListAsync();
        }

        public IList<ActionOfFunction> GetFunctionByRoleAndData(int accessStatusId, int dataId)
        {
            var context = GetContext();

            var query = context.ActionOfFunction.Where(p => p.AccessStatusId == accessStatusId && p.DataId == dataId && p.CompanyId == null && p.UserId == null && p.IsStatus != Constants.DELETE_RECORD && p.IsStatus != Constants.PRE_DELETE_RECORD);

            var actionOfFunctions = query.ToList();

            CtxDisposeOrNot(context);

            return actionOfFunctions;
        }

        public async Task<IList<ActionOfFunction>> GetByCompanyAndAccessAsync(int companyId, int? groupId, int accessId)
        {
            var query = this.ScopedMMSContext.Set<ActionOfFunction>()
                .Where(p => p.CompanyId == companyId
                    && p.AccessStatusId == accessId
                    && p.UserId == null
                    && p.IsStatus != Constants.DELETE_RECORD
                    && p.IsStatus != Constants.PRE_DELETE_RECORD);

            query = groupId != null
                ? query.Where(p => p.GroupId == groupId)
                : query.Where(p => p.GroupId == null);

            var result = await query.ToListAsync();

            return result;
        }

        public async Task<IList<ActionOfFunction>> GetRestrictedIncludeDeleteAsync(int companyId, int accessId, int userId, int? groupId)
        {
            var query = this.ScopedMMSContext.Set<ActionOfFunction>()
                .Where(p => p.CompanyId == companyId
                            && p.AccessStatusId == accessId
                            && p.GroupId == groupId
                            && p.UserId == userId);

            var result = await query.ToListAsync();

            return result;
        }

        public async Task<IList<ActionOfFunction>> GetTemplateWithRestricted(int companyId, int accessId, int userId, int? groupId)
        {
            var query = this.ScopedMMSContext.Set<ActionOfFunction>()
                .Where(p => p.CompanyId == companyId
                            && p.AccessStatusId == accessId
                            && p.IsStatus != Constants.DELETE_RECORD
                            && p.IsStatus != Constants.PRE_DELETE_RECORD);

            query = groupId != null
                ? query.Where(p => p.GroupId == groupId)
                : query.Where(p => p.GroupId == null);

            query = query.Where(p => p.UserId == null || p.UserId == userId);

            var result = await query.ToListAsync();

            return result;
        }
    }
}
